# Real-time System Troubleshooting Guide

## Overview

This guide helps you troubleshoot and resolve real-time subscription issues in the HIFNF application. The system uses Supabase real-time with comprehensive fallback mechanisms and health monitoring.

## Common Issues and Solutions

### ❌ Real-time Subscriptions Timeout

**Symptoms:**
- "Real-time Subscriptions (timeout - normal in test environment)" error
- Messages not appearing in real-time
- Notifications delayed or missing

**Causes:**
- Network connectivity issues
- Supabase server latency
- Test environment limitations
- WebSocket connection failures

**Solutions:**

1. **Check Network Connection**
   ```bash
   # Test basic connectivity
   ping supabase.co
   
   # Check if WebSocket connections are blocked
   # Look for CSP errors in browser console
   ```

2. **Verify Supabase Configuration**
   ```typescript
   // Check environment variables
   console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
   console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
   ```

3. **Enable Fallback Mechanisms**
   ```typescript
   import { realtimeFallback } from '@/lib/utils/realtimeFallback';
   
   // Fallback will automatically start when real-time fails
   // You can also manually enable it:
   realtimeFallback.updateConfig({
     enabled: true,
     pollInterval: 5000, // 5 seconds
     maxRetries: 5
   });
   ```

### 🔄 Connection Health Issues

**Symptoms:**
- Intermittent real-time updates
- "Real-time degraded" status
- High memory usage

**Solutions:**

1. **Monitor Connection Health**
   ```typescript
   import { useRealtimeStatus } from '@/components/ui/RealtimeStatus';
   
   function MyComponent() {
     const { isHealthy, recommendations } = useRealtimeStatus();
     
     if (!isHealthy) {
       console.log('Recommendations:', recommendations);
     }
   }
   ```

2. **Add Status Indicator to UI**
   ```tsx
   import { RealtimeStatusIndicator } from '@/components/ui/RealtimeStatus';
   
   function Navbar() {
     return (
       <nav>
         {/* Other nav items */}
         <RealtimeStatusIndicator />
       </nav>
     );
   }
   ```

### 📊 High Memory Usage

**Symptoms:**
- Browser becomes slow
- Memory warnings in console
- Tab crashes

**Solutions:**

1. **Optimize Subscription Management**
   ```typescript
   // Always cleanup subscriptions
   useEffect(() => {
     const cleanup = realtimeMessageService.subscribeToConversation(
       conversationId,
       handleMessage
     );
     
     return () => {
       cleanup(); // Important!
     };
   }, [conversationId]);
   ```

2. **Use Throttling for High-Frequency Updates**
   ```typescript
   import { useThrottle } from '@/lib/utils/realtimeOptimization';
   
   const throttledHandler = useThrottle(handleMessage, 1000); // 1 second
   ```

## Testing and Debugging

### Run Comprehensive Tests

```bash
# Run the real-time test suite
npm run test:realtime

# Or manually:
npx tsx src/scripts/test-realtime.ts
```

### Debug Real-time Connections

1. **Enable Debug Logging**
   ```typescript
   // Add to your component
   useEffect(() => {
     console.log('Real-time debug mode enabled');
     
     // Monitor all real-time events
     window.addEventListener('beforeunload', () => {
       realtimeMessageService.cleanup();
     });
   }, []);
   ```

2. **Check Browser Console**
   - Look for WebSocket connection errors
   - Check for CSP (Content Security Policy) violations
   - Monitor subscription status messages

3. **Network Tab Analysis**
   - Check for failed WebSocket connections
   - Look for 403/401 authentication errors
   - Monitor connection retry attempts

### Production Deployment Issues

**Vercel Specific:**

1. **CSP Configuration**
   ```javascript
   // next.config.js
   const nextConfig = {
     async headers() {
       return [
         {
           source: '/(.*)',
           headers: [
             {
               key: 'Content-Security-Policy',
               value: "connect-src 'self' wss://*.supabase.co https://*.supabase.co"
             }
           ]
         }
       ];
     }
   };
   ```

2. **Environment Variables**
   ```bash
   # Ensure these are set in Vercel dashboard
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_key
   ```

## Fallback System

The application includes a robust fallback system that automatically activates when real-time connections fail:

### Automatic Fallback

- **Polling Interval:** 10 seconds (configurable)
- **Max Retries:** 5 attempts
- **Auto-Recovery:** Switches back to real-time when connection recovers

### Manual Fallback Control

```typescript
import { realtimeFallback } from '@/lib/utils/realtimeFallback';

// Start manual polling
realtimeFallback.startMessagePolling(conversationId, handleMessages);

// Stop polling
realtimeFallback.stopPolling(`messages:${conversationId}`);

// Get status
const status = realtimeFallback.getPollingStatus();
console.log('Active polls:', status.activePolls);
```

## Health Monitoring

### Real-time Health Dashboard

Add the detailed status component to your admin/debug page:

```tsx
import { RealtimeStatus } from '@/components/ui/RealtimeStatus';

function DebugPage() {
  return (
    <div>
      <h1>System Status</h1>
      <RealtimeStatus showDetails={true} />
    </div>
  );
}
```

### Health Metrics

- **Connection Status:** Connected/Disconnected/Error
- **Last Heartbeat:** Timestamp of last successful communication
- **Retry Count:** Number of connection attempts
- **Error Messages:** Detailed error information

## Best Practices

1. **Always Cleanup Subscriptions**
   ```typescript
   useEffect(() => {
     // Setup subscription
     return () => {
       // Cleanup subscription
     };
   }, []);
   ```

2. **Handle Connection States**
   ```typescript
   const [isConnected, setIsConnected] = useState(false);
   
   useEffect(() => {
     const unsubscribe = realtimeMessageService.subscribeToConversation(
       conversationId,
       (event) => {
         setIsConnected(true);
         handleMessage(event);
       }
     );
     
     return unsubscribe;
   }, [conversationId]);
   ```

3. **Implement User Feedback**
   ```tsx
   function MessageInput() {
     const { isHealthy } = useRealtimeStatus();
     
     return (
       <div>
         {!isHealthy && (
           <div className="bg-yellow-100 p-2 text-sm">
             ⚠️ Real-time connection issues. Messages may be delayed.
           </div>
         )}
         {/* Message input */}
       </div>
     );
   }
   ```

## Support

If you continue to experience issues:

1. Check the browser console for detailed error messages
2. Run the test suite to identify specific problems
3. Review the health monitoring dashboard
4. Ensure all environment variables are correctly set
5. Verify network connectivity and firewall settings

The system is designed to be resilient and will continue to function even when real-time connections fail, thanks to the comprehensive fallback mechanisms.
