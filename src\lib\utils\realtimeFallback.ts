/**
 * Real-time fallback mechanism
 * Provides polling-based updates when real-time connections fail
 */

import React from 'react';
import { getSupabaseClient } from '@/lib/supabase/client';
import { realtimeHealthMonitor } from './realtimeHealthMonitor';

export interface FallbackConfig {
  pollInterval: number; // milliseconds
  maxRetries: number;
  enabled: boolean;
}

export interface PollCallback<T> {
  (data: T[]): void;
}

class RealtimeFallback {
  private pollingIntervals: Map<string, NodeJS.Timeout> = new Map();
  private lastPolledData: Map<string, any[]> = new Map();
  private supabase = getSupabaseClient();
  private defaultConfig: FallbackConfig = {
    pollInterval: 10000, // 10 seconds
    maxRetries: 5,
    enabled: true,
  };

  /**
   * Start polling for messages when real-time fails
   */
  startMessagePolling(
    conversationId: string,
    onNewMessages: PollCallback<any>,
    config: Partial<FallbackConfig> = {}
  ): void {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    if (!finalConfig.enabled) {
      console.log('📊 Fallback polling disabled for conversation:', conversationId);
      return;
    }

    // Check if real-time is healthy first
    if (realtimeHealthMonitor.isHealthy(`conversation:${conversationId}`)) {
      console.log('📊 Real-time is healthy, skipping fallback for:', conversationId);
      return;
    }

    console.log('📊 Starting fallback polling for conversation:', conversationId);
    
    const pollKey = `messages:${conversationId}`;
    this.stopPolling(pollKey);

    let retryCount = 0;
    const poll = async () => {
      try {
        // Check if real-time has recovered
        if (realtimeHealthMonitor.isHealthy(`conversation:${conversationId}`)) {
          console.log('📊 Real-time recovered, stopping fallback polling for:', conversationId);
          this.stopPolling(pollKey);
          return;
        }

        const [senderId, receiverId] = conversationId.split('_');
        const { data: messages, error } = await this.supabase
          .from('messages_realtime')
          .select('*')
          .or(`and(sender_id.eq.${senderId},receiver_id.eq.${receiverId}),and(sender_id.eq.${receiverId},receiver_id.eq.${senderId})`)
          .order('created_at', { ascending: false })
          .limit(50);

        if (error) {
          throw error;
        }

        const lastData = this.lastPolledData.get(pollKey) || [];
        const newMessages = messages?.filter(msg => 
          !lastData.some(lastMsg => lastMsg.id === msg.id)
        ) || [];

        if (newMessages.length > 0) {
          console.log(`📊 Found ${newMessages.length} new messages via polling`);
          onNewMessages(newMessages);
        }

        this.lastPolledData.set(pollKey, messages || []);
        retryCount = 0; // Reset retry count on success

      } catch (error) {
        retryCount++;
        console.error(`📊 Polling error for ${conversationId} (attempt ${retryCount}):`, error);
        
        if (retryCount >= finalConfig.maxRetries) {
          console.error(`📊 Max polling retries reached for ${conversationId}, stopping fallback`);
          this.stopPolling(pollKey);
          return;
        }
      }

      // Schedule next poll
      const interval = setTimeout(poll, finalConfig.pollInterval);
      this.pollingIntervals.set(pollKey, interval);
    };

    // Start polling immediately
    poll();
  }

  /**
   * Start polling for notifications when real-time fails
   */
  startNotificationPolling(
    userId: string,
    onNewNotifications: PollCallback<any>,
    config: Partial<FallbackConfig> = {}
  ): void {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    if (!finalConfig.enabled) {
      console.log('📊 Fallback polling disabled for notifications:', userId);
      return;
    }

    // Check if real-time is healthy first
    if (realtimeHealthMonitor.isHealthy('notifications')) {
      console.log('📊 Real-time notifications are healthy, skipping fallback');
      return;
    }

    console.log('📊 Starting fallback polling for notifications:', userId);
    
    const pollKey = `notifications:${userId}`;
    this.stopPolling(pollKey);

    let retryCount = 0;
    const poll = async () => {
      try {
        // Check if real-time has recovered
        if (realtimeHealthMonitor.isHealthy('notifications')) {
          console.log('📊 Real-time notifications recovered, stopping fallback polling');
          this.stopPolling(pollKey);
          return;
        }

        const { data: notifications, error } = await this.supabase
          .from('notifications_realtime')
          .select('*')
          .eq('recipient_id', userId)
          .order('created_at', { ascending: false })
          .limit(20);

        if (error) {
          throw error;
        }

        const lastData = this.lastPolledData.get(pollKey) || [];
        const newNotifications = notifications?.filter(notif => 
          !lastData.some(lastNotif => lastNotif.id === notif.id)
        ) || [];

        if (newNotifications.length > 0) {
          console.log(`📊 Found ${newNotifications.length} new notifications via polling`);
          onNewNotifications(newNotifications);
        }

        this.lastPolledData.set(pollKey, notifications || []);
        retryCount = 0; // Reset retry count on success

      } catch (error) {
        retryCount++;
        console.error(`📊 Notification polling error (attempt ${retryCount}):`, error);
        
        if (retryCount >= finalConfig.maxRetries) {
          console.error(`📊 Max notification polling retries reached, stopping fallback`);
          this.stopPolling(pollKey);
          return;
        }
      }

      // Schedule next poll
      const interval = setTimeout(poll, finalConfig.pollInterval);
      this.pollingIntervals.set(pollKey, interval);
    };

    // Start polling immediately
    poll();
  }

  /**
   * Stop polling for a specific key
   */
  stopPolling(pollKey: string): void {
    const interval = this.pollingIntervals.get(pollKey);
    if (interval) {
      clearTimeout(interval);
      this.pollingIntervals.delete(pollKey);
      this.lastPolledData.delete(pollKey);
      console.log('📊 Stopped fallback polling for:', pollKey);
    }
  }

  /**
   * Stop all polling
   */
  stopAllPolling(): void {
    this.pollingIntervals.forEach((interval, key) => {
      clearTimeout(interval);
      console.log('📊 Stopped fallback polling for:', key);
    });
    this.pollingIntervals.clear();
    this.lastPolledData.clear();
  }

  /**
   * Get active polling status
   */
  getPollingStatus(): {
    activePolls: string[];
    totalPolls: number;
  } {
    return {
      activePolls: Array.from(this.pollingIntervals.keys()),
      totalPolls: this.pollingIntervals.size,
    };
  }

  /**
   * Update polling configuration
   */
  updateConfig(newConfig: Partial<FallbackConfig>): void {
    this.defaultConfig = { ...this.defaultConfig, ...newConfig };
    console.log('📊 Updated fallback config:', this.defaultConfig);
  }
}

// Export singleton instance
export const realtimeFallback = new RealtimeFallback();

/**
 * React hook for fallback status
 */
export function useFallbackStatus() {
  const [status, setStatus] = React.useState(realtimeFallback.getPollingStatus());

  React.useEffect(() => {
    const interval = setInterval(() => {
      setStatus(realtimeFallback.getPollingStatus());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return status;
}
