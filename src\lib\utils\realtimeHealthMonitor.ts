/**
 * Real-time connection health monitor
 * Monitors Supabase real-time connections and provides fallback mechanisms
 */

import React from 'react';
import { getSupabaseClient } from '@/lib/supabase/client';

export interface ConnectionHealth {
  isConnected: boolean;
  lastHeartbeat: Date | null;
  connectionAttempts: number;
  lastError: string | null;
}

class RealtimeHealthMonitor {
  private healthStatus: Map<string, ConnectionHealth> = new Map();
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private supabase = getSupabaseClient();

  /**
   * Start monitoring a connection
   */
  startMonitoring(channelId: string): void {
    this.healthStatus.set(channelId, {
      isConnected: false,
      lastHeartbeat: null,
      connectionAttempts: 0,
      lastError: null,
    });

    // Start heartbeat monitoring if not already running
    if (!this.heartbeatInterval) {
      this.startHeartbeat();
    }
  }

  /**
   * Update connection status
   */
  updateStatus(channelId: string, status: 'connected' | 'disconnected' | 'error', error?: string): void {
    const health = this.healthStatus.get(channelId);
    if (!health) return;

    switch (status) {
      case 'connected':
        health.isConnected = true;
        health.lastHeartbeat = new Date();
        health.lastError = null;
        break;
      case 'disconnected':
        health.isConnected = false;
        break;
      case 'error':
        health.isConnected = false;
        health.lastError = error || 'Unknown error';
        health.connectionAttempts++;
        break;
    }

    this.healthStatus.set(channelId, health);
  }

  /**
   * Get health status for a channel
   */
  getHealth(channelId: string): ConnectionHealth | null {
    return this.healthStatus.get(channelId) || null;
  }

  /**
   * Check if connection is healthy
   */
  isHealthy(channelId: string): boolean {
    const health = this.healthStatus.get(channelId);
    if (!health) return false;

    const now = new Date();
    const lastHeartbeat = health.lastHeartbeat;
    
    // Consider unhealthy if no heartbeat in last 2 minutes
    if (!lastHeartbeat || (now.getTime() - lastHeartbeat.getTime()) > 120000) {
      return false;
    }

    return health.isConnected && health.connectionAttempts < 5;
  }

  /**
   * Get overall health summary
   */
  getOverallHealth(): {
    totalChannels: number;
    healthyChannels: number;
    unhealthyChannels: number;
    lastUpdate: Date;
  } {
    const total = this.healthStatus.size;
    let healthy = 0;

    this.healthStatus.forEach((health, channelId) => {
      if (this.isHealthy(channelId)) {
        healthy++;
      }
    });

    return {
      totalChannels: total,
      healthyChannels: healthy,
      unhealthyChannels: total - healthy,
      lastUpdate: new Date(),
    };
  }

  /**
   * Start heartbeat monitoring
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.performHealthCheck();
    }, 30000); // Check every 30 seconds
  }

  /**
   * Perform health check on all channels
   */
  private async performHealthCheck(): Promise<void> {
    for (const [channelId, health] of this.healthStatus) {
      if (health.isConnected) {
        try {
          // Simple ping to check if Supabase is responsive
          await this.supabase.from('user_presence').select('count').limit(1);
          this.updateStatus(channelId, 'connected');
        } catch (error) {
          console.warn(`Health check failed for channel ${channelId}:`, error);
          this.updateStatus(channelId, 'error', error instanceof Error ? error.message : 'Health check failed');
        }
      }
    }
  }

  /**
   * Stop monitoring a channel
   */
  stopMonitoring(channelId: string): void {
    this.healthStatus.delete(channelId);

    // Stop heartbeat if no channels are being monitored
    if (this.healthStatus.size === 0 && this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Stop all monitoring
   */
  stopAllMonitoring(): void {
    this.healthStatus.clear();
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Get recommendations based on health status
   */
  getRecommendations(): string[] {
    const recommendations: string[] = [];
    const overall = this.getOverallHealth();

    if (overall.unhealthyChannels > 0) {
      recommendations.push('Some real-time connections are unhealthy. Consider refreshing the page.');
    }

    if (overall.totalChannels === 0) {
      recommendations.push('No real-time connections active. Features may be limited.');
    }

    this.healthStatus.forEach((health, channelId) => {
      if (health.connectionAttempts > 3) {
        recommendations.push(`Channel ${channelId} has multiple connection failures. Check network connectivity.`);
      }
    });

    return recommendations;
  }
}

// Export singleton instance
export const realtimeHealthMonitor = new RealtimeHealthMonitor();

/**
 * React hook for monitoring real-time health
 */
export function useRealtimeHealth(channelId?: string) {
  const [health, setHealth] = React.useState<ConnectionHealth | null>(null);
  const [overallHealth, setOverallHealth] = React.useState(realtimeHealthMonitor.getOverallHealth());

  React.useEffect(() => {
    const interval = setInterval(() => {
      if (channelId) {
        setHealth(realtimeHealthMonitor.getHealth(channelId));
      }
      setOverallHealth(realtimeHealthMonitor.getOverallHealth());
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [channelId]);

  return {
    health,
    overallHealth,
    isHealthy: channelId ? realtimeHealthMonitor.isHealthy(channelId) : true,
    recommendations: realtimeHealthMonitor.getRecommendations(),
  };
}
