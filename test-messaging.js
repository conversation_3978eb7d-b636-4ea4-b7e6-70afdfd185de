/**
 * Simple test script to check real-time messaging functionality
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Supabase configuration missing');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testRealtimeMessaging() {
  console.log('🧪 Testing Real-time Messaging...');
  console.log('================================');

  try {
    // Test 1: Check Supabase connection
    console.log('1. Testing Supabase connection...');
    const { data, error } = await supabase.from('user_presence').select('count').limit(1);
    if (error) {
      console.error('❌ Connection failed:', error.message);
      return;
    }
    console.log('✅ Supabase connection successful');

    // Test 2: Test real-time subscription
    console.log('\n2. Testing real-time subscription...');
    let messageReceived = false;
    
    const channel = supabase
      .channel('test-messages')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'messages_realtime'
      }, (payload) => {
        console.log('📨 Real-time message received:', payload.new);
        messageReceived = true;
      })
      .subscribe((status) => {
        console.log('📡 Subscription status:', status);
      });

    // Wait for subscription to be ready
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test 3: Send a test message
    console.log('\n3. Sending test message...');
    const testMessage = {
      mysql_id: `test-${Date.now()}`,
      sender_id: 'test-sender-123',
      receiver_id: 'test-receiver-456',
      content: 'Hello! This is a test message for real-time functionality.'
    };

    const { error: insertError } = await supabase
      .from('messages_realtime')
      .insert(testMessage);

    if (insertError) {
      console.error('❌ Failed to send message:', insertError.message);
      return;
    }
    console.log('✅ Test message sent successfully');

    // Wait for real-time event
    console.log('\n4. Waiting for real-time event...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    if (messageReceived) {
      console.log('✅ Real-time messaging is working!');
    } else {
      console.log('⚠️ Real-time event not received (may be normal in some environments)');
    }

    // Test 4: Check message exists in database
    console.log('\n5. Verifying message in database...');
    const { data: messages, error: selectError } = await supabase
      .from('messages_realtime')
      .select('*')
      .eq('mysql_id', testMessage.mysql_id);

    if (selectError) {
      console.error('❌ Failed to query messages:', selectError.message);
      return;
    }

    if (messages && messages.length > 0) {
      console.log('✅ Message found in database');
      console.log('📄 Message details:', messages[0]);
    } else {
      console.log('❌ Message not found in database');
    }

    // Cleanup
    console.log('\n6. Cleaning up test data...');
    await supabase
      .from('messages_realtime')
      .delete()
      .eq('mysql_id', testMessage.mysql_id);
    console.log('✅ Cleanup completed');

    // Close subscription
    supabase.removeChannel(channel);

    console.log('\n🎉 Real-time messaging test completed!');
    console.log('📊 Summary:');
    console.log('  - Supabase connection: ✅');
    console.log('  - Message sending: ✅');
    console.log('  - Database storage: ✅');
    console.log(`  - Real-time events: ${messageReceived ? '✅' : '⚠️'}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testRealtimeMessaging().then(() => {
  console.log('\n✨ Test completed. You can now check the messages page in your browser.');
  process.exit(0);
}).catch(error => {
  console.error('❌ Test error:', error);
  process.exit(1);
});
