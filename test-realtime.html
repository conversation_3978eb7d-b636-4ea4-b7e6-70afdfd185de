<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time Messaging Test</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Real-time Messaging Test</h1>
        <p>This page tests the real-time messaging functionality of your application.</p>
        
        <div class="status info">
            <strong>Status:</strong> <span id="status">Initializing...</span>
        </div>
        
        <div>
            <button onclick="testConnection()">Test Connection</button>
            <button onclick="testSubscription()">Test Subscription</button>
            <button onclick="sendTestMessage()">Send Test Message</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <h3>Test Log:</h3>
        <div id="log"></div>
    </div>

    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://xzrqnkapgxfmobrioang.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh6cnFua2FwZ3hmbW9icmlvYW5nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDY0NDAsImV4cCI6MjA2ODQyMjQ0MH0.Hz3YNL4V4-GQuAp4FcoSFffiNK9cOuMhoP4UTqcrmMk';
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        let channel = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.parentElement.className = `status ${type}`;
        }
        
        async function testConnection() {
            log('🔗 Testing Supabase connection...');
            updateStatus('Testing connection...', 'info');
            
            try {
                const { data, error } = await supabase.from('user_presence').select('count').limit(1);
                if (error) {
                    throw error;
                }
                log('✅ Supabase connection successful!');
                updateStatus('Connected', 'success');
            } catch (error) {
                log(`❌ Connection failed: ${error.message}`);
                updateStatus('Connection failed', 'error');
            }
        }
        
        async function testSubscription() {
            log('📡 Testing real-time subscription...');
            updateStatus('Testing subscription...', 'info');
            
            try {
                // Remove existing channel if any
                if (channel) {
                    supabase.removeChannel(channel);
                }
                
                channel = supabase
                    .channel('test-messages-' + Date.now())
                    .on('postgres_changes', {
                        event: 'INSERT',
                        schema: 'public',
                        table: 'messages_realtime'
                    }, (payload) => {
                        log(`📨 Real-time message received: ${JSON.stringify(payload.new)}`);
                        updateStatus('Real-time working!', 'success');
                    })
                    .subscribe((status) => {
                        log(`📡 Subscription status: ${status}`);
                        if (status === 'SUBSCRIBED') {
                            log('✅ Real-time subscription active!');
                            updateStatus('Subscribed to real-time', 'success');
                        } else if (status === 'CHANNEL_ERROR') {
                            log('❌ Subscription error');
                            updateStatus('Subscription error', 'error');
                        }
                    });
                    
            } catch (error) {
                log(`❌ Subscription failed: ${error.message}`);
                updateStatus('Subscription failed', 'error');
            }
        }
        
        async function sendTestMessage() {
            log('📤 Sending test message...');
            updateStatus('Sending message...', 'info');
            
            try {
                const testMessage = {
                    mysql_id: `test-web-${Date.now()}`,
                    sender_id: 'test-sender-web',
                    receiver_id: 'test-receiver-web',
                    content: `Test message from web at ${new Date().toLocaleTimeString()}`
                };
                
                const { error } = await supabase
                    .from('messages_realtime')
                    .insert(testMessage);
                
                if (error) {
                    throw error;
                }
                
                log('✅ Test message sent successfully!');
                log(`📄 Message: ${testMessage.content}`);
                updateStatus('Message sent', 'success');
                
                // Wait a bit to see if real-time event is received
                setTimeout(() => {
                    log('⏳ Waiting for real-time event...');
                }, 1000);
                
            } catch (error) {
                log(`❌ Failed to send message: ${error.message}`);
                updateStatus('Send failed', 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Initialize
        window.addEventListener('load', () => {
            log('🚀 Real-time messaging test page loaded');
            updateStatus('Ready for testing', 'info');
            
            // Auto-test connection
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
