require('dotenv/config');
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkSchema() {
  console.log('🔍 Checking Supabase table schemas...\n');
  
  // Check messages_realtime structure
  console.log('📨 Checking messages_realtime...');
  try {
    const { data: msgData, error: msgError } = await supabase
      .from('messages_realtime')
      .select('*')
      .limit(1);
      
    if (msgError) {
      console.log('❌ Error:', msgError.message);
    } else {
      console.log('✅ Table accessible');
      if (msgData && msgData.length > 0) {
        console.log('📋 Sample columns:', Object.keys(msgData[0]));
      } else {
        console.log('📋 No data, but table exists');
      }
    }
  } catch (error) {
    console.log('❌ Exception:', error.message);
  }
  
  console.log('\n👥 Checking user_presence...');
  try {
    const { data: presenceData, error: presenceError } = await supabase
      .from('user_presence')
      .select('*')
      .limit(1);
      
    if (presenceError) {
      console.log('❌ Error:', presenceError.message);
    } else {
      console.log('✅ Table accessible');
      if (presenceData && presenceData.length > 0) {
        console.log('📋 Sample columns:', Object.keys(presenceData[0]));
      } else {
        console.log('📋 No data, but table exists');
      }
    }
  } catch (error) {
    console.log('❌ Exception:', error.message);
  }
  
  console.log('\n🔔 Checking notifications_realtime...');
  try {
    const { data: notifData, error: notifError } = await supabase
      .from('notifications_realtime')
      .select('*')
      .limit(1);
      
    if (notifError) {
      console.log('❌ Error:', notifError.message);
    } else {
      console.log('✅ Table accessible');
      if (notifData && notifData.length > 0) {
        console.log('📋 Sample columns:', Object.keys(notifData[0]));
      } else {
        console.log('📋 No data, but table exists');
      }
    }
  } catch (error) {
    console.log('❌ Exception:', error.message);
  }
  
  // Test real-time subscription
  console.log('\n🔄 Testing real-time subscription...');
  try {
    const channel = supabase
      .channel('test-channel')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'user_presence' 
      }, (payload) => {
        console.log('📡 Real-time event received:', payload);
      })
      .subscribe((status) => {
        console.log('📡 Subscription status:', status);
        if (status === 'SUBSCRIBED') {
          console.log('✅ Real-time subscription working!');
          setTimeout(() => {
            supabase.removeChannel(channel);
            console.log('🔚 Test completed!\n');
          }, 2000);
        }
      });
  } catch (error) {
    console.log('❌ Real-time error:', error.message);
  }
}

checkSchema().catch(console.error);
