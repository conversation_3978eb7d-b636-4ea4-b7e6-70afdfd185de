'use client';

import React, { useState, useEffect } from 'react';
import { realtimeHealthMonitor } from '@/lib/utils/realtimeHealthMonitor';
import { realtimeFallback, useFallbackStatus } from '@/lib/utils/realtimeFallback';

interface RealtimeStatusProps {
  showDetails?: boolean;
  className?: string;
}

export function RealtimeStatus({ showDetails = false, className = '' }: RealtimeStatusProps) {
  const [overallHealth, setOverallHealth] = useState(realtimeHealthMonitor.getOverallHealth());
  const [recommendations, setRecommendations] = useState<string[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const fallbackStatus = useFallbackStatus();

  useEffect(() => {
    const interval = setInterval(() => {
      setOverallHealth(realtimeHealthMonitor.getOverallHealth());
      setRecommendations(realtimeHealthMonitor.getRecommendations());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = () => {
    const healthyRatio = overallHealth.healthyChannels / Math.max(overallHealth.totalChannels, 1);
    
    if (healthyRatio >= 0.8) return 'text-green-500';
    if (healthyRatio >= 0.5) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getStatusIcon = () => {
    const healthyRatio = overallHealth.healthyChannels / Math.max(overallHealth.totalChannels, 1);
    
    if (healthyRatio >= 0.8) return '🟢';
    if (healthyRatio >= 0.5) return '🟡';
    return '🔴';
  };

  const getStatusText = () => {
    if (overallHealth.totalChannels === 0) return 'No connections';
    
    const healthyRatio = overallHealth.healthyChannels / overallHealth.totalChannels;
    
    if (healthyRatio >= 0.8) return 'Healthy';
    if (healthyRatio >= 0.5) return 'Degraded';
    return 'Unhealthy';
  };

  if (!showDetails && overallHealth.totalChannels === 0) {
    return null; // Don't show anything if no connections and not showing details
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      <div 
        className="p-3 cursor-pointer flex items-center justify-between"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-2">
          <span className="text-lg">{getStatusIcon()}</span>
          <span className={`font-medium ${getStatusColor()}`}>
            Real-time: {getStatusText()}
          </span>
          {fallbackStatus.totalPolls > 0 && (
            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
              Fallback Active
            </span>
          )}
        </div>
        
        {showDetails && (
          <span className="text-gray-400">
            {isExpanded ? '▼' : '▶'}
          </span>
        )}
      </div>

      {showDetails && isExpanded && (
        <div className="border-t border-gray-200 p-3 space-y-3">
          {/* Connection Stats */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="text-gray-600">Total Connections</div>
              <div className="font-medium">{overallHealth.totalChannels}</div>
            </div>
            <div>
              <div className="text-gray-600">Healthy</div>
              <div className="font-medium text-green-600">{overallHealth.healthyChannels}</div>
            </div>
            <div>
              <div className="text-gray-600">Unhealthy</div>
              <div className="font-medium text-red-600">{overallHealth.unhealthyChannels}</div>
            </div>
            <div>
              <div className="text-gray-600">Last Update</div>
              <div className="font-medium text-xs">
                {overallHealth.lastUpdate.toLocaleTimeString()}
              </div>
            </div>
          </div>

          {/* Fallback Status */}
          {fallbackStatus.totalPolls > 0 && (
            <div className="bg-blue-50 p-2 rounded">
              <div className="text-sm font-medium text-blue-800 mb-1">
                Fallback Polling Active
              </div>
              <div className="text-xs text-blue-600">
                {fallbackStatus.totalPolls} active poll{fallbackStatus.totalPolls !== 1 ? 's' : ''}
              </div>
              <div className="text-xs text-blue-600 mt-1">
                Channels: {fallbackStatus.activePolls.join(', ')}
              </div>
            </div>
          )}

          {/* Recommendations */}
          {recommendations.length > 0 && (
            <div className="bg-yellow-50 p-2 rounded">
              <div className="text-sm font-medium text-yellow-800 mb-1">
                Recommendations
              </div>
              <ul className="text-xs text-yellow-700 space-y-1">
                {recommendations.map((rec, index) => (
                  <li key={index}>• {rec}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-2">
            <button
              onClick={() => window.location.reload()}
              className="text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
            >
              Refresh Page
            </button>
            <button
              onClick={() => {
                realtimeFallback.stopAllPolling();
                setIsExpanded(false);
              }}
              className="text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600"
              disabled={fallbackStatus.totalPolls === 0}
            >
              Stop Fallback
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Compact status indicator for navbar or sidebar
 */
export function RealtimeStatusIndicator({ className = '' }: { className?: string }) {
  const [overallHealth, setOverallHealth] = useState(realtimeHealthMonitor.getOverallHealth());
  const fallbackStatus = useFallbackStatus();

  useEffect(() => {
    const interval = setInterval(() => {
      setOverallHealth(realtimeHealthMonitor.getOverallHealth());
    }, 10000); // Update every 10 seconds for indicator

    return () => clearInterval(interval);
  }, []);

  if (overallHealth.totalChannels === 0) {
    return null;
  }

  const healthyRatio = overallHealth.healthyChannels / overallHealth.totalChannels;
  let statusColor = 'bg-green-500';
  let statusText = 'Real-time active';

  if (healthyRatio < 0.8) {
    statusColor = 'bg-yellow-500';
    statusText = 'Real-time degraded';
  }
  
  if (healthyRatio < 0.5) {
    statusColor = 'bg-red-500';
    statusText = 'Real-time issues';
  }

  if (fallbackStatus.totalPolls > 0) {
    statusColor = 'bg-blue-500';
    statusText = 'Using fallback';
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`} title={statusText}>
      <div className={`w-2 h-2 rounded-full ${statusColor}`}></div>
      {fallbackStatus.totalPolls > 0 && (
        <span className="text-xs text-gray-600">Fallback</span>
      )}
    </div>
  );
}

/**
 * Hook for components that need real-time status
 */
export function useRealtimeStatus() {
  const [overallHealth, setOverallHealth] = useState(realtimeHealthMonitor.getOverallHealth());
  const [recommendations, setRecommendations] = useState<string[]>([]);
  const fallbackStatus = useFallbackStatus();

  useEffect(() => {
    const interval = setInterval(() => {
      setOverallHealth(realtimeHealthMonitor.getOverallHealth());
      setRecommendations(realtimeHealthMonitor.getRecommendations());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const isHealthy = overallHealth.totalChannels === 0 || 
    (overallHealth.healthyChannels / overallHealth.totalChannels) >= 0.8;

  return {
    overallHealth,
    recommendations,
    fallbackStatus,
    isHealthy,
    hasFallback: fallbackStatus.totalPolls > 0,
  };
}
