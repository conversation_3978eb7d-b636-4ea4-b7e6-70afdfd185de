/**
 * Supabase Real-time Tables Setup
 * Creates minimal tables for real-time messaging and notifications
 * Run this script to setup Supabase database for real-time features
 */

import 'dotenv/config';
import { getSupabaseServiceClient } from '../lib/supabase/client';

const SQL_COMMANDS = [
  // Enable Row Level Security
  `ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';`,

  // Create messages_realtime table
  `CREATE TABLE IF NOT EXISTS public.messages_realtime (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    mysql_message_id VARCHAR(255) NOT NULL,
    sender_id VARCHAR(255) NOT NULL,
    receiver_id VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(mysql_message_id)
  );`,

  // Create notifications_realtime table
  `CREATE TABLE IF NOT EXISTS public.notifications_realtime (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    mysql_notification_id VARCHAR(255) NOT NULL,
    recipient_id VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(mysql_notification_id)
  );`,

  // Create user_presence table
  `CREATE TABLE IF NOT EXISTS public.user_presence (
    user_id VARCHAR(255) PRIMARY KEY,
    status VARCHAR(20) DEFAULT 'offline' CHECK (status IN ('online', 'offline', 'away')),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    typing_to VARCHAR(255) DEFAULT NULL,
    typing_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
  );`,

  // Create indexes for better performance
  `CREATE INDEX IF NOT EXISTS idx_messages_realtime_sender ON public.messages_realtime(sender_id);`,
  `CREATE INDEX IF NOT EXISTS idx_messages_realtime_receiver ON public.messages_realtime(receiver_id);`,
  `CREATE INDEX IF NOT EXISTS idx_messages_realtime_created ON public.messages_realtime(created_at DESC);`,
  `CREATE INDEX IF NOT EXISTS idx_notifications_realtime_recipient ON public.notifications_realtime(recipient_id);`,
  `CREATE INDEX IF NOT EXISTS idx_notifications_realtime_created ON public.notifications_realtime(created_at DESC);`,
  `CREATE INDEX IF NOT EXISTS idx_user_presence_status ON public.user_presence(status);`,

  // Enable Row Level Security
  `ALTER TABLE public.messages_realtime ENABLE ROW LEVEL SECURITY;`,
  `ALTER TABLE public.notifications_realtime ENABLE ROW LEVEL SECURITY;`,
  `ALTER TABLE public.user_presence ENABLE ROW LEVEL SECURITY;`,

  // Create RLS policies for messages_realtime
  `CREATE POLICY "Users can view their own messages" ON public.messages_realtime
    FOR SELECT USING (auth.uid()::text = sender_id OR auth.uid()::text = receiver_id);`,
  
  `CREATE POLICY "Users can insert their own messages" ON public.messages_realtime
    FOR INSERT WITH CHECK (auth.uid()::text = sender_id);`,

  `CREATE POLICY "Users can update their own messages" ON public.messages_realtime
    FOR UPDATE USING (auth.uid()::text = sender_id OR auth.uid()::text = receiver_id);`,

  // Create RLS policies for notifications_realtime
  `CREATE POLICY "Users can view their own notifications" ON public.notifications_realtime
    FOR SELECT USING (auth.uid()::text = recipient_id);`,

  `CREATE POLICY "Service role can insert notifications" ON public.notifications_realtime
    FOR INSERT WITH CHECK (true);`,

  `CREATE POLICY "Users can update their own notifications" ON public.notifications_realtime
    FOR UPDATE USING (auth.uid()::text = recipient_id);`,

  // Create RLS policies for user_presence
  `CREATE POLICY "Users can view all presence" ON public.user_presence
    FOR SELECT USING (true);`,

  `CREATE POLICY "Users can update their own presence" ON public.user_presence
    FOR ALL USING (auth.uid()::text = user_id);`,

  // Create function to cleanup old messages (keep only last 7 days)
  `CREATE OR REPLACE FUNCTION cleanup_old_realtime_messages()
  RETURNS void AS $$
  BEGIN
    DELETE FROM public.messages_realtime 
    WHERE created_at < NOW() - INTERVAL '7 days';
    
    DELETE FROM public.notifications_realtime 
    WHERE created_at < NOW() - INTERVAL '30 days';
  END;
  $$ LANGUAGE plpgsql;`,

  // Create function to update user presence
  `CREATE OR REPLACE FUNCTION update_user_presence(
    p_user_id VARCHAR(255),
    p_status VARCHAR(20) DEFAULT 'online'
  )
  RETURNS void AS $$
  BEGIN
    INSERT INTO public.user_presence (user_id, status, last_seen)
    VALUES (p_user_id, p_status, NOW())
    ON CONFLICT (user_id) 
    DO UPDATE SET 
      status = EXCLUDED.status,
      last_seen = NOW();
  END;
  $$ LANGUAGE plpgsql;`,
];

async function setupSupabaseTables() {
  try {
    console.log('🚀 Setting up Supabase real-time tables...');
    
    const supabase = getSupabaseServiceClient();

    for (let i = 0; i < SQL_COMMANDS.length; i++) {
      const command = SQL_COMMANDS[i];
      console.log(`📝 Executing command ${i + 1}/${SQL_COMMANDS.length}...`);
      
      const { error } = await supabase.rpc('exec_sql', { sql: command });
      
      if (error) {
        console.error(`❌ Error executing command ${i + 1}:`, error);
        // Continue with other commands
      } else {
        console.log(`✅ Command ${i + 1} executed successfully`);
      }
    }

    console.log('🎉 Supabase real-time tables setup completed!');
    console.log('\n📋 Created tables:');
    console.log('  - messages_realtime (for real-time messaging)');
    console.log('  - notifications_realtime (for real-time notifications)');
    console.log('  - user_presence (for online status & typing indicators)');
    console.log('\n🔒 Row Level Security enabled with appropriate policies');
    console.log('📈 Performance indexes created');
    console.log('🧹 Cleanup functions created');

  } catch (error) {
    console.error('❌ Failed to setup Supabase tables:', error);
    process.exit(1);
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  setupSupabaseTables();
}

export { setupSupabaseTables };
