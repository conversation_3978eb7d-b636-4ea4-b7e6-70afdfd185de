/**
 * Realtime Health Check Utility
 * Checks if Supabase realtime is working properly and provides fallback mechanisms
 */

import { getSupabaseClient } from '../supabase/client';

interface HealthCheckResult {
  isHealthy: boolean;
  error?: string;
  canUseRealtime: boolean;
}

class RealtimeHealthChecker {
  private isHealthy: boolean = true;
  private lastCheck: number = 0;
  private checkInterval: number = 5 * 60 * 1000; // 5 minutes
  private transportError: boolean = false;

  /**
   * Check if realtime is healthy
   */
  async checkHealth(): Promise<HealthCheckResult> {
    const now = Date.now();
    
    // Skip check if recently checked and healthy
    if (this.isHealthy && (now - this.lastCheck) < this.checkInterval) {
      return {
        isHealthy: this.isHealthy,
        canUseRealtime: this.isHealthy && !this.transportError,
      };
    }

    this.lastCheck = now;

    try {
      const supabase = getSupabaseClient();
      
      // Try to create a simple channel to test realtime
      const testChannel = supabase.channel('health-check');
      
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          testChannel.unsubscribe();
          this.isHealthy = false;
          resolve({
            isHealthy: false,
            error: 'Realtime connection timeout',
            canUseRealtime: false,
          });
        }, 5000);

        testChannel.subscribe((status) => {
          clearTimeout(timeout);
          
          if (status === 'SUBSCRIBED') {
            this.isHealthy = true;
            this.transportError = false;
            testChannel.unsubscribe();
            resolve({
              isHealthy: true,
              canUseRealtime: true,
            });
          } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            this.isHealthy = false;
            testChannel.unsubscribe();
            resolve({
              isHealthy: false,
              error: `Realtime status: ${status}`,
              canUseRealtime: false,
            });
          }
        });
      });

    } catch (error) {
      this.isHealthy = false;
      
      // Check for transport constructor error
      if (error instanceof Error && error.message.includes('transport is not a constructor')) {
        this.transportError = true;
        console.error('❌ Supabase transport constructor error detected');
      }

      return {
        isHealthy: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        canUseRealtime: false,
      };
    }
  }

  /**
   * Mark transport as having errors
   */
  markTransportError() {
    this.transportError = true;
    this.isHealthy = false;
  }

  /**
   * Check if we can use realtime features
   */
  canUseRealtime(): boolean {
    return this.isHealthy && !this.transportError;
  }

  /**
   * Reset health status
   */
  reset() {
    this.isHealthy = true;
    this.transportError = false;
    this.lastCheck = 0;
  }
}

// Export singleton instance
export const realtimeHealthChecker = new RealtimeHealthChecker();

/**
 * Quick check if realtime is available
 */
export async function isRealtimeAvailable(): Promise<boolean> {
  try {
    const result = await realtimeHealthChecker.checkHealth();
    return result.canUseRealtime;
  } catch {
    return false;
  }
}

/**
 * Disable realtime features due to transport error
 */
export function disableRealtimeFeatures() {
  realtimeHealthChecker.markTransportError();
  console.warn('🚫 Realtime features have been disabled due to transport errors');
}
